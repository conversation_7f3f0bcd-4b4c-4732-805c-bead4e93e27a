"use client"

import { useState } from "react"
import dynamic from "next/dynamic"
import MainLayout from "@/components/layout/main-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Menu, Layers, Filter, Maximize, Navigation, Download, Share2 } from "lucide-react"

// 动态导入全屏地图组件
const FullScreenMap = dynamic(() => import("@/components/map/full-screen-map"), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full bg-gray-100 animate-pulse flex items-center justify-center">
      <div className="text-gray-500">加载中国海域地图...</div>
    </div>
  ),
})

export default function MapPage() {
  const [isControlPanelOpen, setIsControlPanelOpen] = useState(true)
  const [selectedMapService, setSelectedMapService] = useState("amap")
  const [heatmapOpacity, setHeatmapOpacity] = useState([70])
  const [showHeatmap, setShowHeatmap] = useState(true)
  const [showDistribution, setShowDistribution] = useState(true)
  const [showCollectionPoints, setShowCollectionPoints] = useState(true)
  const [selectedSpecies, setSelectedSpecies] = useState("all")
  const [selectedTimeRange, setSelectedTimeRange] = useState("all")
  const [hideVillageMarkers, setHideVillageMarkers] = useState(true) // 新增状态

  const mapServices = [
    { value: "amap", label: "高德地图", description: "中国地图服务（推荐）" },
  ]

  const speciesCategories = [
    { value: "all", label: "全部物种" },
    { value: "cetaceans", label: "鲸豚类" },
    { value: "fish", label: "鱼类" },
    { value: "crustaceans", label: "甲壳动物" },
    { value: "mollusks", label: "软体动物" },
  ]

  return (
    <MainLayout>
      <div className="relative h-screen">
        {/* Full Screen Map */}
        <FullScreenMap
          mapService={selectedMapService}
          showHeatmap={showHeatmap}
          showDistribution={showDistribution}
          showCollectionPoints={showCollectionPoints}
          heatmapOpacity={heatmapOpacity[0]}
          selectedSpecies={selectedSpecies}
          selectedTimeRange={selectedTimeRange}
          hideVillageMarkers={hideVillageMarkers}
          showMarkerLayers={true}
        />

        {/* Top Controls */}
        <div className="absolute top-4 left-4 z-10 flex space-x-2">
          <Button
            variant="outline"
            size="icon"
            className="bg-white/90 backdrop-blur-sm"
            onClick={() => setIsControlPanelOpen(!isControlPanelOpen)}
          >
            <Menu className="w-4 h-4" />
          </Button>

          <Select value={selectedMapService} onValueChange={setSelectedMapService}>
            <SelectTrigger className="w-48 bg-white/90 backdrop-blur-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {mapServices.map((service) => (
                <SelectItem key={service.value} value={service.value}>
                  <div>
                    <div className="font-medium">{service.label}</div>
                    <div className="text-xs text-gray-500">{service.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Right Control Panel */}
        <div
          className={`absolute top-4 right-4 bottom-4 z-10 transition-transform duration-300 ${
            isControlPanelOpen ? "translate-x-0" : "translate-x-full"
          }`}
        >
          <Card className="w-80 h-full map-control-panel overflow-y-auto">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">中国海域分析</CardTitle>
                <Button variant="ghost" size="icon" onClick={() => setIsControlPanelOpen(false)}>
                  ×
                </Button>
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Layer Controls */}
              <div>
                <h3 className="font-semibold mb-3 flex items-center">
                  <Layers className="w-4 h-4 mr-2" />
                  图层控制
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <label className="text-sm">海域热力图</label>
                    <Switch checked={showHeatmap} onCheckedChange={setShowHeatmap} />
                  </div>

                  {showHeatmap && (
                    <div className="ml-4">
                      <label className="text-xs text-gray-600 mb-2 block">透明度: {heatmapOpacity[0]}%</label>
                      <Slider
                        value={heatmapOpacity}
                        onValueChange={setHeatmapOpacity}
                        max={100}
                        step={10}
                        className="w-full"
                      />
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <label className="text-sm">物种分布区</label>
                    <Switch checked={showDistribution} onCheckedChange={setShowDistribution} />
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-sm">监测站点</label>
                    <Switch checked={showCollectionPoints} onCheckedChange={setShowCollectionPoints} />
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-sm">隐藏村庄标记</label>
                    <Switch checked={hideVillageMarkers} onCheckedChange={setHideVillageMarkers} />
                  </div>
                </div>
              </div>

              {/* Filter Controls */}
              <div>
                <h3 className="font-semibold mb-3 flex items-center">
                  <Filter className="w-4 h-4 mr-2" />
                  筛选控制
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm mb-2 block">物种分类</label>
                    <Select value={selectedSpecies} onValueChange={setSelectedSpecies}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {speciesCategories.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm mb-2 block">时间范围</label>
                    <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部时间</SelectItem>
                        <SelectItem value="recent">最近一年</SelectItem>
                        <SelectItem value="2023">2023年</SelectItem>
                        <SelectItem value="2022">2022年</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Statistics */}
              <div>
                <h3 className="font-semibold mb-3">中国海域统计</h3>
                <div className="grid grid-cols-2 gap-3">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-lg font-bold text-blue-600">2,156</div>
                    <div className="text-xs text-gray-600">海洋物种</div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-lg font-bold text-green-600">8,934</div>
                    <div className="text-xs text-gray-600">声学记录</div>
                  </div>
                  <div className="text-center p-3 bg-orange-50 rounded-lg">
                    <div className="text-lg font-bold text-orange-600">156</div>
                    <div className="text-xs text-gray-600">监测站点</div>
                  </div>
                  <div className="text-center p-3 bg-purple-50 rounded-lg">
                    <div className="text-lg font-bold text-purple-600">28</div>
                    <div className="text-xs text-gray-600">重点海域</div>
                  </div>
                </div>
              </div>

              {/* Legend */}
              <div>
                <h3 className="font-semibold mb-3">海域密度图例</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-red-600 border"></div>
                    <span className="text-sm">450+ 种（极高密度）</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-orange-600 border"></div>
                    <span className="text-sm">400-450 种（高密度）</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-orange-500 border"></div>
                    <span className="text-sm">350-400 种</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-yellow-500 border"></div>
                    <span className="text-sm">300-350 种</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-yellow-400 border"></div>
                    <span className="text-sm">250-300 种</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-lime-500 border"></div>
                    <span className="text-sm">200-250 种</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-green-500 border"></div>
                    <span className="text-sm">150-200 种</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-emerald-500 border"></div>
                    <span className="text-sm">100-150 种（中低密度）</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                    <span className="text-sm">监测站点</span>
                  </div>
                </div>
              </div>

              {/* Sea Areas */}
              <div>
                <h3 className="font-semibold mb-3">主要海域</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>渤海湾</span>
                    <Badge variant="secondary">高密度</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>黄海</span>
                    <Badge variant="secondary">极高密度</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>东海</span>
                    <Badge variant="secondary">极高密度</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>台湾海峡</span>
                    <Badge variant="secondary">高密度</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>南海</span>
                    <Badge variant="secondary">极高密度</Badge>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div>
                <h3 className="font-semibold mb-3">快速操作</h3>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <Navigation className="w-4 h-4 mr-2" />
                    定位到中国海域
                  </Button>
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <Maximize className="w-4 h-4 mr-2" />
                    全屏显示
                  </Button>
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <Download className="w-4 h-4 mr-2" />
                    导出海域数据
                  </Button>
                  <Button variant="outline" className="w-full justify-start bg-transparent">
                    <Share2 className="w-4 h-4 mr-2" />
                    分享地图
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Bottom Info Bar */}
        <div className="absolute bottom-4 left-4 right-4 z-10">
          <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <div className="text-sm">
                <span className="text-gray-600">坐标:</span>
                <span className="ml-1 font-mono">35.0000°N, 105.0000°E</span>
              </div>
              <div className="text-sm">
                <span className="text-gray-600">缩放级别:</span>
                <span className="ml-1">4</span>
              </div>
              <div className="text-sm">
                <span className="text-gray-600">覆盖范围:</span>
                <span className="ml-1">中国海域（含台湾）</span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Badge variant="secondary">实时数据</Badge>
              <div className="text-xs text-gray-500">最后更新: 2024-01-15 14:30</div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
