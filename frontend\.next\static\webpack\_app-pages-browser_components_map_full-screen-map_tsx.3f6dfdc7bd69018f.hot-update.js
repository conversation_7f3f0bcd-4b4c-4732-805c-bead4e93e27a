"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_components_map_full-screen-map_tsx",{

/***/ "(app-pages-browser)/./components/map/full-screen-map.tsx":
/*!********************************************!*\
  !*** ./components/map/full-screen-map.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FullScreenMap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var leaflet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! leaflet */ \"(app-pages-browser)/./node_modules/.pnpm/leaflet@1.9.4/node_modules/leaflet/dist/leaflet-src.js\");\n/* harmony import */ var leaflet__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(leaflet__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var leaflet_dist_leaflet_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! leaflet/dist/leaflet.css */ \"(app-pages-browser)/./node_modules/.pnpm/leaflet@1.9.4/node_modules/leaflet/dist/leaflet.css\");\n/* harmony import */ var _marker_layer_manager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./marker-layer-manager */ \"(app-pages-browser)/./components/map/marker-layer-manager.tsx\");\n/* harmony import */ var _layer_control_panel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./layer-control-panel */ \"(app-pages-browser)/./components/map/layer-control-panel.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction FullScreenMap(param) {\n    let { mapService, showHeatmap, showDistribution, showCollectionPoints, heatmapOpacity, selectedSpecies, selectedTimeRange, hideVillageMarkers = true, showMarkerLayers = true } = param;\n    _s();\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mapInstanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const layersRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const markerManager = (0,_marker_layer_manager__WEBPACK_IMPORTED_MODULE_4__.useMarkerLayerManager)(mapInstanceRef.current);\n    const [showLayerControl, setShowLayerControl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullScreenMap.useEffect\": ()=>{\n            if (!mapRef.current || mapInstanceRef.current) return;\n            // 初始化地图 - 聚焦中国区域\n            const map = leaflet__WEBPACK_IMPORTED_MODULE_2___default().map(mapRef.current, {\n                center: [\n                    35.0,\n                    105.0\n                ],\n                zoom: 4,\n                zoomControl: false,\n                attributionControl: false,\n                minZoom: 3,\n                maxZoom: 18\n            });\n            // 添加缩放控制器到右下角\n            leaflet__WEBPACK_IMPORTED_MODULE_2___default().control.zoom({\n                position: \"bottomright\"\n            }).addTo(map);\n            // 监听缩放事件，更新数据属性\n            map.on('zoomend', {\n                \"FullScreenMap.useEffect\": ()=>{\n                    if (mapRef.current && hideVillageMarkers) {\n                        const currentZoom = map.getZoom();\n                        const zoomLevel = currentZoom >= 10 ? 'high' : currentZoom >= 6 ? 'medium' : 'low';\n                        mapRef.current.setAttribute('data-zoom-level', zoomLevel);\n                    }\n                }\n            }[\"FullScreenMap.useEffect\"]);\n            mapInstanceRef.current = map;\n            // 初始化marker层数据\n            if (markerManager && showMarkerLayers) {\n                initializeMarkerLayers();\n            }\n            return ({\n                \"FullScreenMap.useEffect\": ()=>{\n                    if (mapInstanceRef.current) {\n                        mapInstanceRef.current.remove();\n                        mapInstanceRef.current = null;\n                    }\n                }\n            })[\"FullScreenMap.useEffect\"];\n        }\n    }[\"FullScreenMap.useEffect\"], [\n        hideVillageMarkers\n    ]);\n    // 初始化marker层数据\n    const initializeMarkerLayers = ()=>{\n        if (!markerManager) return;\n        // 海洋生物采集点层\n        const collectionLayer = {\n            id: \"marine-collection-points\",\n            name: \"海洋生物采集点\",\n            visible: showCollectionPoints,\n            color: \"#3388ff\",\n            icon: _marker_layer_manager__WEBPACK_IMPORTED_MODULE_4__.markerIcons.collection,\n            markers: [\n                {\n                    id: \"collection-1\",\n                    lat: 39.0,\n                    lng: 117.8,\n                    title: \"渤海湾采集点\",\n                    description: \"江豚声学监测站\",\n                    category: \"采集点\",\n                    popupContent: '\\n            <div class=\"p-3 min-w-48\">\\n              <h4 class=\"font-semibold mb-2 text-blue-800\">渤海湾采集点</h4>\\n              <div class=\"space-y-2 text-sm\">\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">目标物种:</span>\\n                  <span class=\"font-medium\">江豚</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">声学记录:</span>\\n                  <span class=\"font-medium\">45条</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">最近更新:</span>\\n                  <span class=\"font-medium\">2024-01-15</span>\\n                </div>\\n              </div>\\n            </div>\\n          '\n                },\n                {\n                    id: \"collection-2\",\n                    lat: 31.2,\n                    lng: 121.8,\n                    title: \"长江口采集点\",\n                    description: \"中华白海豚监测站\",\n                    category: \"采集点\"\n                },\n                {\n                    id: \"collection-3\",\n                    lat: 24.5,\n                    lng: 118.2,\n                    title: \"台湾海峡采集点\",\n                    description: \"台湾白海豚监测站\",\n                    category: \"采集点\"\n                },\n                {\n                    id: \"collection-4\",\n                    lat: 22.3,\n                    lng: 114.1,\n                    title: \"珠江口采集点\",\n                    description: \"中华白海豚保护区\",\n                    category: \"采集点\"\n                },\n                {\n                    id: \"collection-5\",\n                    lat: 20.2,\n                    lng: 110.8,\n                    title: \"海南北部采集点\",\n                    description: \"热带海豚监测站\",\n                    category: \"采集点\"\n                },\n                {\n                    id: \"collection-6\",\n                    lat: 16.8,\n                    lng: 112.3,\n                    title: \"西沙群岛采集点\",\n                    description: \"深海鲸类监测站\",\n                    category: \"采集点\"\n                }\n            ]\n        };\n        // 科研观测点层\n        const researchLayer = {\n            id: \"research-stations\",\n            name: \"海洋科研观测站\",\n            visible: false,\n            color: \"#f59e0b\",\n            icon: _marker_layer_manager__WEBPACK_IMPORTED_MODULE_4__.markerIcons.research,\n            markers: [\n                {\n                    id: \"research-1\",\n                    lat: 38.5,\n                    lng: 119.0,\n                    title: \"渤海海洋科学研究站\",\n                    description: \"海洋生态系统研究\",\n                    category: \"科研站\"\n                },\n                {\n                    id: \"research-2\",\n                    lat: 30.0,\n                    lng: 122.0,\n                    title: \"东海海洋研究所\",\n                    description: \"海洋生物多样性研究\",\n                    category: \"科研站\"\n                },\n                {\n                    id: \"research-3\",\n                    lat: 21.0,\n                    lng: 112.0,\n                    title: \"南海海洋科学院\",\n                    description: \"深海生物研究中心\",\n                    category: \"科研站\"\n                }\n            ]\n        };\n        // 环境监测点层\n        const monitoringLayer = {\n            id: \"monitoring-stations\",\n            name: \"海洋环境监测站\",\n            visible: false,\n            color: \"#ef4444\",\n            icon: _marker_layer_manager__WEBPACK_IMPORTED_MODULE_4__.markerIcons.monitoring,\n            markers: [\n                {\n                    id: \"monitoring-1\",\n                    lat: 40.0,\n                    lng: 120.5,\n                    title: \"辽东湾监测站\",\n                    description: \"水质与声环境监测\",\n                    category: \"监测站\"\n                },\n                {\n                    id: \"monitoring-2\",\n                    lat: 36.0,\n                    lng: 120.0,\n                    title: \"黄海南部监测站\",\n                    description: \"海洋噪音监测\",\n                    category: \"监测站\"\n                },\n                {\n                    id: \"monitoring-3\",\n                    lat: 28.0,\n                    lng: 121.0,\n                    title: \"东海南部监测站\",\n                    description: \"船舶噪音影响监测\",\n                    category: \"监测站\"\n                },\n                {\n                    id: \"monitoring-4\",\n                    lat: 18.5,\n                    lng: 109.5,\n                    title: \"海南西部监测站\",\n                    description: \"渔业活动监测\",\n                    category: \"监测站\"\n                }\n            ]\n        };\n        // 添加所有marker层\n        markerManager.addMarkerLayer(collectionLayer);\n        markerManager.addMarkerLayer(researchLayer);\n        markerManager.addMarkerLayer(monitoringLayer);\n        setShowLayerControl(true);\n    };\n    // 更新底图 - 使用中国地图服务\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullScreenMap.useEffect\": ()=>{\n            if (!mapInstanceRef.current) return;\n            const map = mapInstanceRef.current;\n            // 移除现有底图\n            map.eachLayer({\n                \"FullScreenMap.useEffect\": (layer)=>{\n                    if (layer instanceof (leaflet__WEBPACK_IMPORTED_MODULE_2___default().TileLayer)) {\n                        map.removeLayer(layer);\n                    }\n                }\n            }[\"FullScreenMap.useEffect\"]);\n            // 添加中国地图服务\n            let tileUrl = \"\";\n            let attribution = \"\";\n            // 使用高德地图 - 中国地图服务，正确显示台湾地区\n            tileUrl = \"https://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=7&x={x}&y={y}&z={z}\";\n            attribution = \"© 高德地图\";\n            // 根据不同地图服务配置subdomains\n            let subdomains = [\n                \"a\",\n                \"b\",\n                \"c\"\n            ];\n            if (mapService === \"amap\" || mapService === \"amap-satellite\") {\n                subdomains = [\n                    \"1\",\n                    \"2\",\n                    \"3\",\n                    \"4\"\n                ];\n            } else if (mapService === \"tencent\") {\n                subdomains = [\n                    \"0\",\n                    \"1\",\n                    \"2\",\n                    \"3\"\n                ];\n            } else if (mapService === \"tianditu\") {\n                subdomains = [\n                    \"0\",\n                    \"1\",\n                    \"2\",\n                    \"3\",\n                    \"4\",\n                    \"5\",\n                    \"6\",\n                    \"7\"\n                ];\n            }\n            const tileLayerOptions = {\n                attribution,\n                maxZoom: 18,\n                subdomains,\n                // 添加CSS类名以便后续样式处理\n                className: hideVillageMarkers ? 'hide-village-markers' : ''\n            };\n            leaflet__WEBPACK_IMPORTED_MODULE_2___default().tileLayer(tileUrl, tileLayerOptions).addTo(map);\n        }\n    }[\"FullScreenMap.useEffect\"], [\n        mapService,\n        hideVillageMarkers\n    ]);\n    // 更新热力图层 - 聚焦中国海域\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullScreenMap.useEffect\": ()=>{\n            if (!mapInstanceRef.current) return;\n            const map = mapInstanceRef.current;\n            // 移除现有热力图层\n            if (layersRef.current.heatmap) {\n                map.removeLayer(layersRef.current.heatmap);\n            }\n            if (showHeatmap) {\n                const heatmapLayer = leaflet__WEBPACK_IMPORTED_MODULE_2___default().layerGroup();\n                // 中国海域热力图实现\n                const createChinaMarineHeatmap = {\n                    \"FullScreenMap.useEffect.createChinaMarineHeatmap\": ()=>{\n                        const currentZoom = map.getZoom();\n                        const bounds = map.getBounds();\n                        // 根据缩放级别动态调整网格大小\n                        let gridSize = 1.0 // 基础网格大小\n                        ;\n                        if (currentZoom >= 8) gridSize = 0.2;\n                        else if (currentZoom >= 6) gridSize = 0.4;\n                        else if (currentZoom >= 4) gridSize = 0.8;\n                        else gridSize = 1.2;\n                        // 中国海域热点数据 - 包含台湾海域\n                        const chinaMarineHotspots = [\n                            // 渤海湾区域\n                            {\n                                lat: 39.0,\n                                lng: 117.5,\n                                intensity: 0.8,\n                                radius: 4,\n                                name: \"渤海湾\"\n                            },\n                            {\n                                lat: 38.5,\n                                lng: 119.0,\n                                intensity: 0.7,\n                                radius: 3,\n                                name: \"渤海中部\"\n                            },\n                            {\n                                lat: 40.0,\n                                lng: 120.5,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"辽东湾\"\n                            },\n                            // 黄海区域\n                            {\n                                lat: 36.0,\n                                lng: 120.0,\n                                intensity: 0.9,\n                                radius: 5,\n                                name: \"黄海南部\"\n                            },\n                            {\n                                lat: 35.0,\n                                lng: 119.5,\n                                intensity: 0.8,\n                                radius: 4,\n                                name: \"黄海中部\"\n                            },\n                            {\n                                lat: 37.5,\n                                lng: 122.0,\n                                intensity: 0.7,\n                                radius: 3,\n                                name: \"黄海北部\"\n                            },\n                            // 东海区域\n                            {\n                                lat: 31.0,\n                                lng: 122.0,\n                                intensity: 0.9,\n                                radius: 5,\n                                name: \"长江口\"\n                            },\n                            {\n                                lat: 29.5,\n                                lng: 122.5,\n                                intensity: 0.8,\n                                radius: 4,\n                                name: \"东海中部\"\n                            },\n                            {\n                                lat: 28.0,\n                                lng: 121.0,\n                                intensity: 0.7,\n                                radius: 4,\n                                name: \"东海南部\"\n                            },\n                            {\n                                lat: 30.0,\n                                lng: 123.5,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"东海外海\"\n                            },\n                            // 台湾海峡及周边海域\n                            {\n                                lat: 24.5,\n                                lng: 118.5,\n                                intensity: 0.8,\n                                radius: 4,\n                                name: \"台湾海峡北部\"\n                            },\n                            {\n                                lat: 23.5,\n                                lng: 118.0,\n                                intensity: 0.7,\n                                radius: 3,\n                                name: \"台湾海峡中部\"\n                            },\n                            {\n                                lat: 22.5,\n                                lng: 117.5,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"台湾海峡南部\"\n                            },\n                            // 台湾东部海域\n                            {\n                                lat: 24.0,\n                                lng: 121.5,\n                                intensity: 0.8,\n                                radius: 4,\n                                name: \"台湾东海岸\"\n                            },\n                            {\n                                lat: 23.0,\n                                lng: 121.3,\n                                intensity: 0.7,\n                                radius: 3,\n                                name: \"花莲外海\"\n                            },\n                            {\n                                lat: 22.0,\n                                lng: 121.0,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"台东外海\"\n                            },\n                            // 南海北部区域\n                            {\n                                lat: 22.0,\n                                lng: 114.0,\n                                intensity: 0.9,\n                                radius: 5,\n                                name: \"珠江口\"\n                            },\n                            {\n                                lat: 21.0,\n                                lng: 112.0,\n                                intensity: 0.8,\n                                radius: 4,\n                                name: \"南海北部\"\n                            },\n                            {\n                                lat: 20.0,\n                                lng: 111.0,\n                                intensity: 0.7,\n                                radius: 4,\n                                name: \"海南北部\"\n                            },\n                            {\n                                lat: 18.5,\n                                lng: 109.5,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"海南西部\"\n                            },\n                            // 南海中部区域\n                            {\n                                lat: 16.0,\n                                lng: 112.0,\n                                intensity: 0.7,\n                                radius: 4,\n                                name: \"西沙群岛\"\n                            },\n                            {\n                                lat: 15.0,\n                                lng: 113.5,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"南海中部\"\n                            },\n                            {\n                                lat: 12.0,\n                                lng: 114.0,\n                                intensity: 0.5,\n                                radius: 3,\n                                name: \"南沙群岛北部\"\n                            },\n                            // 南海南部区域\n                            {\n                                lat: 9.0,\n                                lng: 114.5,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"南沙群岛\"\n                            },\n                            {\n                                lat: 8.0,\n                                lng: 112.0,\n                                intensity: 0.5,\n                                radius: 2,\n                                name: \"南海南部\"\n                            },\n                            // 钓鱼岛及周边海域\n                            {\n                                lat: 25.7,\n                                lng: 123.5,\n                                intensity: 0.7,\n                                radius: 2,\n                                name: \"钓鱼岛海域\"\n                            }\n                        ];\n                        // 创建网格映射以避免重复瓦片\n                        const gridMap = new Map();\n                        // 处理每个热点并生成周围的网格瓦片\n                        chinaMarineHotspots.forEach({\n                            \"FullScreenMap.useEffect.createChinaMarineHeatmap\": (hotspot)=>{\n                                const maxRadius = Math.min(hotspot.radius, 6);\n                                for(let latOffset = -maxRadius; latOffset <= maxRadius; latOffset++){\n                                    for(let lngOffset = -maxRadius; lngOffset <= maxRadius; lngOffset++){\n                                        const distance = Math.sqrt(latOffset * latOffset + lngOffset * lngOffset);\n                                        if (distance <= maxRadius) {\n                                            const cellLat = hotspot.lat + latOffset * gridSize;\n                                            const cellLng = hotspot.lng + lngOffset * gridSize;\n                                            // 对齐到网格\n                                            const gridLat = Math.round(cellLat / gridSize) * gridSize;\n                                            const gridLng = Math.round(cellLng / gridSize) * gridSize;\n                                            const gridKey = \"\".concat(gridLat.toFixed(3), \"_\").concat(gridLng.toFixed(3));\n                                            // 基于距离计算强度\n                                            const distanceRatio = 1 - distance / maxRadius;\n                                            // 使用位置坐标作为种子生成固定的伪随机值，避免Math.random()\n                                            const seed = (gridLat * 1000 + gridLng * 1000) % 1000;\n                                            const pseudoRandom = (seed * 9301 + 49297) % 233280 / 233280;\n                                            const cellIntensity = hotspot.intensity * distanceRatio * (0.5 + pseudoRandom * 0.5);\n                                            // 只保留有意义的强度值\n                                            if (cellIntensity >= 0.3) {\n                                                if (gridMap.has(gridKey)) {\n                                                    const existing = gridMap.get(gridKey);\n                                                    existing.intensity = Math.min(1.0, existing.intensity + cellIntensity * 0.4);\n                                                    existing.count++;\n                                                } else {\n                                                    gridMap.set(gridKey, {\n                                                        intensity: cellIntensity,\n                                                        count: 1,\n                                                        name: hotspot.name\n                                                    });\n                                                }\n                                            }\n                                        }\n                                    }\n                                }\n                            }\n                        }[\"FullScreenMap.useEffect.createChinaMarineHeatmap\"]);\n                        // 渲染网格瓦片\n                        const tileBatch = [];\n                        gridMap.forEach({\n                            \"FullScreenMap.useEffect.createChinaMarineHeatmap\": (data, gridKey)=>{\n                                const [latStr, lngStr] = gridKey.split(\"_\");\n                                const gridLat = Number.parseFloat(latStr);\n                                const gridLng = Number.parseFloat(lngStr);\n                                // 跳过低强度瓦片 - 只显示有颜色的\n                                if (data.intensity < 0.35) return;\n                                // 检查是否在当前视图范围内\n                                const buffer = gridSize * 2;\n                                if (gridLat < bounds.getSouth() - buffer || gridLat > bounds.getNorth() + buffer || gridLng < bounds.getWest() - buffer || gridLng > bounds.getEast() + buffer) {\n                                    return;\n                                }\n                                // 定义方形边界\n                                const tileBounds = [\n                                    [\n                                        gridLat - gridSize / 2,\n                                        gridLng - gridSize / 2\n                                    ],\n                                    [\n                                        gridLat + gridSize / 2,\n                                        gridLng + gridSize / 2\n                                    ]\n                                ];\n                                // 颜色映射 - 去除白色和浅色\n                                let color = \"#10b981\" // 默认翠绿色\n                                ;\n                                let speciesCount = Math.floor(data.intensity * 500);\n                                if (data.intensity >= 0.9) {\n                                    color = \"#dc2626\" // 红色 450+\n                                    ;\n                                    speciesCount = Math.floor(450 + data.intensity * 150);\n                                } else if (data.intensity >= 0.8) {\n                                    color = \"#ea580c\" // 橙红色 400-450\n                                    ;\n                                } else if (data.intensity >= 0.7) {\n                                    color = \"#f97316\" // 橙色 350-400\n                                    ;\n                                } else if (data.intensity >= 0.6) {\n                                    color = \"#f59e0b\" // 琥珀色 300-350\n                                    ;\n                                } else if (data.intensity >= 0.5) {\n                                    color = \"#eab308\" // 黄色 250-300\n                                    ;\n                                } else if (data.intensity >= 0.45) {\n                                    color = \"#84cc16\" // 青柠色 200-250\n                                    ;\n                                } else if (data.intensity >= 0.4) {\n                                    color = \"#22c55e\" // 绿色 150-200\n                                    ;\n                                } else {\n                                    color = \"#10b981\" // 翠绿色 100-150\n                                    ;\n                                }\n                                // 创建矩形瓦片\n                                const rectangle = leaflet__WEBPACK_IMPORTED_MODULE_2___default().rectangle(tileBounds, {\n                                    color: color,\n                                    fillColor: color,\n                                    fillOpacity: Math.min(data.intensity * (heatmapOpacity / 100), 0.8),\n                                    weight: 0,\n                                    stroke: false,\n                                    interactive: true\n                                });\n                                // 悬停效果\n                                rectangle.on(\"mouseover\", {\n                                    \"FullScreenMap.useEffect.createChinaMarineHeatmap\": function() {\n                                        this.setStyle({\n                                            fillOpacity: Math.min(data.intensity * (heatmapOpacity / 100) + 0.15, 0.9)\n                                        });\n                                    }\n                                }[\"FullScreenMap.useEffect.createChinaMarineHeatmap\"]);\n                                rectangle.on(\"mouseout\", {\n                                    \"FullScreenMap.useEffect.createChinaMarineHeatmap\": function() {\n                                        this.setStyle({\n                                            fillOpacity: Math.min(data.intensity * (heatmapOpacity / 100), 0.8)\n                                        });\n                                    }\n                                }[\"FullScreenMap.useEffect.createChinaMarineHeatmap\"]);\n                                // 详细弹窗\n                                rectangle.bindPopup('\\n            <div class=\"p-3 min-w-52\">\\n              <h4 class=\"font-semibold mb-2 text-blue-800\">'.concat(data.name, '海域</h4>\\n              <div class=\"space-y-2 text-sm\">\\n                <div class=\"flex justify-between items-center\">\\n                  <span class=\"text-gray-600\">海洋物种:</span>\\n                  <span class=\"font-bold text-lg\" style=\"color: ').concat(color, '\">').concat(speciesCount, '种</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">声学记录:</span>\\n                  <span class=\"font-medium\">').concat(Math.floor(speciesCount * 2.8), '条</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">生物密度:</span>\\n                  <span class=\"font-medium\">').concat(data.intensity >= 0.8 ? \"极高\" : data.intensity >= 0.6 ? \"高\" : data.intensity >= 0.45 ? \"中\" : \"中低\", '</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">海域坐标:</span>\\n                  <span class=\"font-mono text-xs\">').concat(gridLat.toFixed(2), \"\\xb0N, \").concat(gridLng.toFixed(2), '\\xb0E</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">监测站点:</span>\\n                  <span class=\"text-xs\">').concat(data.count, '个</span>\\n                </div>\\n              </div>\\n              <div class=\"mt-3 flex space-x-2\">\\n                <button class=\"px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors\">\\n                  详细数据\\n                </button>\\n                <button class=\"px-3 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600 transition-colors\">\\n                  播放录音\\n                </button>\\n              </div>\\n            </div>\\n          '), {\n                                    maxWidth: 320,\n                                    className: \"custom-popup\"\n                                });\n                                tileBatch.push(rectangle);\n                            }\n                        }[\"FullScreenMap.useEffect.createChinaMarineHeatmap\"]);\n                        // 批量添加瓦片\n                        tileBatch.forEach({\n                            \"FullScreenMap.useEffect.createChinaMarineHeatmap\": (tile)=>tile.addTo(heatmapLayer)\n                        }[\"FullScreenMap.useEffect.createChinaMarineHeatmap\"]);\n                        console.log(\"渲染了 \".concat(tileBatch.length, \" 个中国海域热力图瓦片，缩放级别 \").concat(currentZoom));\n                    }\n                }[\"FullScreenMap.useEffect.createChinaMarineHeatmap\"];\n                createChinaMarineHeatmap();\n                // 地图移动或缩放时更新热力图\n                const updateHeatmapHandler = leaflet__WEBPACK_IMPORTED_MODULE_2___default().Util.throttle({\n                    \"FullScreenMap.useEffect.updateHeatmapHandler\": ()=>{\n                        if (showHeatmap && layersRef.current.heatmap) {\n                            map.removeLayer(layersRef.current.heatmap);\n                            const newHeatmapLayer = leaflet__WEBPACK_IMPORTED_MODULE_2___default().layerGroup();\n                            layersRef.current.heatmap = newHeatmapLayer;\n                            createChinaMarineHeatmap();\n                            newHeatmapLayer.addTo(map);\n                        }\n                    }\n                }[\"FullScreenMap.useEffect.updateHeatmapHandler\"], 400);\n                map.on(\"zoomend moveend\", updateHeatmapHandler);\n                layersRef.current.heatmap = heatmapLayer;\n                heatmapLayer.addTo(map);\n                return ({\n                    \"FullScreenMap.useEffect\": ()=>{\n                        map.off(\"zoomend moveend\", updateHeatmapHandler);\n                    }\n                })[\"FullScreenMap.useEffect\"];\n            }\n        }\n    }[\"FullScreenMap.useEffect\"], [\n        showHeatmap,\n        heatmapOpacity,\n        selectedSpecies,\n        selectedTimeRange\n    ]);\n    // 更新分布图层\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullScreenMap.useEffect\": ()=>{\n            if (!mapInstanceRef.current) return;\n            const map = mapInstanceRef.current;\n            if (layersRef.current.distribution) {\n                map.removeLayer(layersRef.current.distribution);\n            }\n            if (showDistribution) {\n                const distributionLayer = leaflet__WEBPACK_IMPORTED_MODULE_2___default().layerGroup();\n                // 中国海域物种分布区域\n                const chinaDistributionAreas = [\n                    {\n                        lat: 38.0,\n                        lng: 119.0,\n                        radius: 300000,\n                        name: \"渤海黄海鲸豚分布区\"\n                    },\n                    {\n                        lat: 30.0,\n                        lng: 122.0,\n                        radius: 400000,\n                        name: \"东海江豚分布区\"\n                    },\n                    {\n                        lat: 24.0,\n                        lng: 118.5,\n                        radius: 350000,\n                        name: \"台湾海峡中华白海豚分布区\"\n                    },\n                    {\n                        lat: 20.0,\n                        lng: 112.0,\n                        radius: 500000,\n                        name: \"南海鲸类分布区\"\n                    }\n                ];\n                chinaDistributionAreas.forEach({\n                    \"FullScreenMap.useEffect\": (area, index)=>{\n                        const colors = [\n                            \"#3b82f6\",\n                            \"#10b981\",\n                            \"#f59e0b\",\n                            \"#ef4444\"\n                        ];\n                        leaflet__WEBPACK_IMPORTED_MODULE_2___default().circle([\n                            area.lat,\n                            area.lng\n                        ], {\n                            color: colors[index % colors.length],\n                            fillColor: colors[index % colors.length],\n                            fillOpacity: 0.15,\n                            radius: area.radius,\n                            weight: 2\n                        }).addTo(distributionLayer).bindPopup('\\n          <div class=\"p-2\">\\n            <h4 class=\"font-semibold\">'.concat(area.name, '</h4>\\n            <p class=\"text-sm text-gray-600\">重要海洋生物栖息和活动区域</p>\\n          </div>\\n        '));\n                    }\n                }[\"FullScreenMap.useEffect\"]);\n                layersRef.current.distribution = distributionLayer;\n                distributionLayer.addTo(map);\n            }\n        }\n    }[\"FullScreenMap.useEffect\"], [\n        showDistribution\n    ]);\n    // 更新采集点图层\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullScreenMap.useEffect\": ()=>{\n            if (!mapInstanceRef.current) return;\n            const map = mapInstanceRef.current;\n            if (layersRef.current.collectionPoints) {\n                map.removeLayer(layersRef.current.collectionPoints);\n            }\n            if (showCollectionPoints) {\n                const collectionPointsLayer = leaflet__WEBPACK_IMPORTED_MODULE_2___default().layerGroup();\n                // 中国海域采集点数据\n                const chinaCollectionPoints = [\n                    {\n                        lat: 39.0,\n                        lng: 117.8,\n                        species: \"江豚\",\n                        recordings: 45,\n                        date: \"2024-01-15\"\n                    },\n                    {\n                        lat: 31.2,\n                        lng: 121.8,\n                        species: \"中华白海豚\",\n                        recordings: 32,\n                        date: \"2024-01-14\"\n                    },\n                    {\n                        lat: 24.5,\n                        lng: 118.2,\n                        species: \"台湾白海豚\",\n                        recordings: 28,\n                        date: \"2024-01-13\"\n                    },\n                    {\n                        lat: 22.3,\n                        lng: 114.1,\n                        species: \"中华白海豚\",\n                        recordings: 38,\n                        date: \"2024-01-12\"\n                    },\n                    {\n                        lat: 20.2,\n                        lng: 110.8,\n                        species: \"热带海豚\",\n                        recordings: 25,\n                        date: \"2024-01-11\"\n                    },\n                    {\n                        lat: 16.8,\n                        lng: 112.3,\n                        species: \"西沙鲸类\",\n                        recordings: 19,\n                        date: \"2024-01-10\"\n                    }\n                ];\n                chinaCollectionPoints.forEach({\n                    \"FullScreenMap.useEffect\": (point)=>{\n                        leaflet__WEBPACK_IMPORTED_MODULE_2___default().marker([\n                            point.lat,\n                            point.lng\n                        ]).addTo(collectionPointsLayer).bindPopup('\\n            <div class=\"p-3 min-w-48\">\\n              <h4 class=\"font-semibold mb-2\">'.concat(point.species, '监测点</h4>\\n              <div class=\"space-y-1 text-sm\">\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">声学记录:</span>\\n                  <span class=\"font-medium\">').concat(point.recordings, '条</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">最近更新:</span>\\n                  <span class=\"font-medium\">').concat(point.date, '</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">坐标:</span>\\n                  <span class=\"font-mono text-xs\">').concat(point.lat.toFixed(2), \"\\xb0N, \").concat(point.lng.toFixed(2), '\\xb0E</span>\\n                </div>\\n              </div>\\n              <div class=\"mt-3 flex space-x-2\">\\n                <button class=\"px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600\">\\n                  播放录音\\n                </button>\\n                <button class=\"px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600\">\\n                  查看详情\\n                </button>\\n              </div>\\n            </div>\\n          '));\n                    }\n                }[\"FullScreenMap.useEffect\"]);\n                layersRef.current.collectionPoints = collectionPointsLayer;\n                collectionPointsLayer.addTo(map);\n            }\n        }\n    }[\"FullScreenMap.useEffect\"], [\n        showCollectionPoints,\n        selectedSpecies,\n        selectedTimeRange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: mapRef,\n                className: \"w-full h-full \".concat(hideVillageMarkers ? 'hide-village-markers' : ''),\n                \"data-map-service\": mapService,\n                \"data-zoom-level\": \"medium\",\n                style: {\n                    // 添加CSS过滤器来隐藏白色方块村庄标记\n                    ...hideVillageMarkers && {\n                        filter: 'contrast(1.1) saturate(1.05)'\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map\\\\full-screen-map.tsx\",\n                lineNumber: 680,\n                columnNumber: 7\n            }, this),\n            showLayerControl && markerManager && showMarkerLayers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-[1000]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layer_control_panel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    markerManager: markerManager,\n                    compact: false,\n                    className: \"w-80 max-h-96 overflow-y-auto\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map\\\\full-screen-map.tsx\",\n                    lineNumber: 696,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map\\\\full-screen-map.tsx\",\n                lineNumber: 695,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map\\\\full-screen-map.tsx\",\n        lineNumber: 679,\n        columnNumber: 5\n    }, this);\n}\n_s(FullScreenMap, \"grOti/9O8OH7VDN7G7UeNn+bnt0=\", false, function() {\n    return [\n        _marker_layer_manager__WEBPACK_IMPORTED_MODULE_4__.useMarkerLayerManager\n    ];\n});\n_c = FullScreenMap;\nvar _c;\n$RefreshReg$(_c, \"FullScreenMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/map/full-screen-map.tsx\n"));

/***/ })

});