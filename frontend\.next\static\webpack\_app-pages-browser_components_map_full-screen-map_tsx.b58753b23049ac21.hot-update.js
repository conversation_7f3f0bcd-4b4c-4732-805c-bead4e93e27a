"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_components_map_full-screen-map_tsx",{

/***/ "(app-pages-browser)/./components/map/full-screen-map.tsx":
/*!********************************************!*\
  !*** ./components/map/full-screen-map.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FullScreenMap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var leaflet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! leaflet */ \"(app-pages-browser)/./node_modules/.pnpm/leaflet@1.9.4/node_modules/leaflet/dist/leaflet-src.js\");\n/* harmony import */ var leaflet__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(leaflet__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var leaflet_dist_leaflet_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! leaflet/dist/leaflet.css */ \"(app-pages-browser)/./node_modules/.pnpm/leaflet@1.9.4/node_modules/leaflet/dist/leaflet.css\");\n/* harmony import */ var _marker_layer_manager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./marker-layer-manager */ \"(app-pages-browser)/./components/map/marker-layer-manager.tsx\");\n/* harmony import */ var _layer_control_panel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./layer-control-panel */ \"(app-pages-browser)/./components/map/layer-control-panel.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction FullScreenMap(param) {\n    let { mapService, showHeatmap, showDistribution, showCollectionPoints, heatmapOpacity, selectedSpecies, selectedTimeRange, hideVillageMarkers = true, showMarkerLayers = true } = param;\n    _s();\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mapInstanceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const layersRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const markerManager = (0,_marker_layer_manager__WEBPACK_IMPORTED_MODULE_4__.useMarkerLayerManager)(mapInstanceRef.current);\n    const [showLayerControl, setShowLayerControl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullScreenMap.useEffect\": ()=>{\n            if (!mapRef.current || mapInstanceRef.current) return;\n            // 初始化地图 - 聚焦中国区域\n            const map = leaflet__WEBPACK_IMPORTED_MODULE_2___default().map(mapRef.current, {\n                center: [\n                    35.0,\n                    105.0\n                ],\n                zoom: 4,\n                zoomControl: false,\n                attributionControl: false,\n                minZoom: 3,\n                maxZoom: 18\n            });\n            // 添加缩放控制器到右下角\n            leaflet__WEBPACK_IMPORTED_MODULE_2___default().control.zoom({\n                position: \"bottomright\"\n            }).addTo(map);\n            // 监听缩放事件，更新数据属性\n            map.on('zoomend', {\n                \"FullScreenMap.useEffect\": ()=>{\n                    if (mapRef.current && hideVillageMarkers) {\n                        const currentZoom = map.getZoom();\n                        const zoomLevel = currentZoom >= 10 ? 'high' : currentZoom >= 6 ? 'medium' : 'low';\n                        mapRef.current.setAttribute('data-zoom-level', zoomLevel);\n                    }\n                }\n            }[\"FullScreenMap.useEffect\"]);\n            mapInstanceRef.current = map;\n            // 初始化marker层数据\n            if (markerManager && showMarkerLayers) {\n                initializeMarkerLayers();\n            }\n            return ({\n                \"FullScreenMap.useEffect\": ()=>{\n                    if (mapInstanceRef.current) {\n                        mapInstanceRef.current.remove();\n                        mapInstanceRef.current = null;\n                    }\n                }\n            })[\"FullScreenMap.useEffect\"];\n        }\n    }[\"FullScreenMap.useEffect\"], [\n        hideVillageMarkers\n    ]);\n    // 初始化marker层数据\n    const initializeMarkerLayers = ()=>{\n        if (!markerManager) return;\n        // 海洋生物采集点层\n        const collectionLayer = {\n            id: \"marine-collection-points\",\n            name: \"海洋生物采集点\",\n            visible: showCollectionPoints,\n            color: \"#3388ff\",\n            icon: _marker_layer_manager__WEBPACK_IMPORTED_MODULE_4__.markerIcons.collection,\n            markers: [\n                {\n                    id: \"collection-1\",\n                    lat: 39.0,\n                    lng: 117.8,\n                    title: \"渤海湾采集点\",\n                    description: \"江豚声学监测站\",\n                    category: \"采集点\",\n                    popupContent: '\\n            <div class=\"p-3 min-w-48\">\\n              <h4 class=\"font-semibold mb-2 text-blue-800\">渤海湾采集点</h4>\\n              <div class=\"space-y-2 text-sm\">\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">目标物种:</span>\\n                  <span class=\"font-medium\">江豚</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">声学记录:</span>\\n                  <span class=\"font-medium\">45条</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">最近更新:</span>\\n                  <span class=\"font-medium\">2024-01-15</span>\\n                </div>\\n              </div>\\n            </div>\\n          '\n                },\n                {\n                    id: \"collection-2\",\n                    lat: 31.2,\n                    lng: 121.8,\n                    title: \"长江口采集点\",\n                    description: \"中华白海豚监测站\",\n                    category: \"采集点\"\n                },\n                {\n                    id: \"collection-3\",\n                    lat: 24.5,\n                    lng: 118.2,\n                    title: \"台湾海峡采集点\",\n                    description: \"台湾白海豚监测站\",\n                    category: \"采集点\"\n                },\n                {\n                    id: \"collection-4\",\n                    lat: 22.3,\n                    lng: 114.1,\n                    title: \"珠江口采集点\",\n                    description: \"中华白海豚保护区\",\n                    category: \"采集点\"\n                },\n                {\n                    id: \"collection-5\",\n                    lat: 20.2,\n                    lng: 110.8,\n                    title: \"海南北部采集点\",\n                    description: \"热带海豚监测站\",\n                    category: \"采集点\"\n                },\n                {\n                    id: \"collection-6\",\n                    lat: 16.8,\n                    lng: 112.3,\n                    title: \"西沙群岛采集点\",\n                    description: \"深海鲸类监测站\",\n                    category: \"采集点\"\n                }\n            ]\n        };\n        // 科研观测点层\n        const researchLayer = {\n            id: \"research-stations\",\n            name: \"海洋科研观测站\",\n            visible: false,\n            color: \"#f59e0b\",\n            icon: _marker_layer_manager__WEBPACK_IMPORTED_MODULE_4__.markerIcons.research,\n            markers: [\n                {\n                    id: \"research-1\",\n                    lat: 38.5,\n                    lng: 119.0,\n                    title: \"渤海海洋科学研究站\",\n                    description: \"海洋生态系统研究\",\n                    category: \"科研站\"\n                },\n                {\n                    id: \"research-2\",\n                    lat: 30.0,\n                    lng: 122.0,\n                    title: \"东海海洋研究所\",\n                    description: \"海洋生物多样性研究\",\n                    category: \"科研站\"\n                },\n                {\n                    id: \"research-3\",\n                    lat: 21.0,\n                    lng: 112.0,\n                    title: \"南海海洋科学院\",\n                    description: \"深海生物研究中心\",\n                    category: \"科研站\"\n                }\n            ]\n        };\n        // 环境监测点层\n        const monitoringLayer = {\n            id: \"monitoring-stations\",\n            name: \"海洋环境监测站\",\n            visible: false,\n            color: \"#ef4444\",\n            icon: _marker_layer_manager__WEBPACK_IMPORTED_MODULE_4__.markerIcons.monitoring,\n            markers: [\n                {\n                    id: \"monitoring-1\",\n                    lat: 40.0,\n                    lng: 120.5,\n                    title: \"辽东湾监测站\",\n                    description: \"水质与声环境监测\",\n                    category: \"监测站\"\n                },\n                {\n                    id: \"monitoring-2\",\n                    lat: 36.0,\n                    lng: 120.0,\n                    title: \"黄海南部监测站\",\n                    description: \"海洋噪音监测\",\n                    category: \"监测站\"\n                },\n                {\n                    id: \"monitoring-3\",\n                    lat: 28.0,\n                    lng: 121.0,\n                    title: \"东海南部监测站\",\n                    description: \"船舶噪音影响监测\",\n                    category: \"监测站\"\n                },\n                {\n                    id: \"monitoring-4\",\n                    lat: 18.5,\n                    lng: 109.5,\n                    title: \"海南西部监测站\",\n                    description: \"渔业活动监测\",\n                    category: \"监测站\"\n                }\n            ]\n        };\n        // 添加所有marker层\n        markerManager.addMarkerLayer(collectionLayer);\n        markerManager.addMarkerLayer(researchLayer);\n        markerManager.addMarkerLayer(monitoringLayer);\n        setShowLayerControl(true);\n    };\n    // 更新底图 - 使用中国地图服务\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullScreenMap.useEffect\": ()=>{\n            if (!mapInstanceRef.current) return;\n            const map = mapInstanceRef.current;\n            // 移除现有底图\n            map.eachLayer({\n                \"FullScreenMap.useEffect\": (layer)=>{\n                    if (layer instanceof (leaflet__WEBPACK_IMPORTED_MODULE_2___default().TileLayer)) {\n                        map.removeLayer(layer);\n                    }\n                }\n            }[\"FullScreenMap.useEffect\"]);\n            // 添加中国地图服务\n            let tileUrl = \"\";\n            let attribution = \"\";\n            switch(mapService){\n                case \"osm\":\n                    // 使用OpenStreetMap但聚焦中国区域\n                    tileUrl = \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\";\n                    attribution = \"© OpenStreetMap contributors\";\n                    break;\n                case \"esri-world\":\n                    tileUrl = \"https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}\";\n                    attribution = \"© Esri\";\n                    break;\n                case \"esri-satellite\":\n                    tileUrl = \"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}\";\n                    attribution = \"© Esri\";\n                    break;\n                case \"cartodb-light\":\n                    tileUrl = \"https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png\";\n                    attribution = \"© CartoDB\";\n                    break;\n                case \"cartodb-dark\":\n                    tileUrl = \"https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png\";\n                    attribution = \"© CartoDB\";\n                    break;\n                case \"osm-no-labels\":\n                    // 使用无标签的OpenStreetMap样式\n                    tileUrl = \"https://{s}.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png\";\n                    attribution = \"© CartoDB, © OpenStreetMap contributors\";\n                    break;\n                case \"tianditu\":\n                    // 天地图服务 - 中国官方地图\n                    tileUrl = \"https://t{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=YOUR_API_KEY\";\n                    attribution = \"© 天地图\";\n                    break;\n                case \"amap\":\n                    // 高德地图 - 更新为最新URL\n                    tileUrl = \"https://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=7&x={x}&y={y}&z={z}\";\n                    attribution = \"© 高德地图\";\n                    break;\n                case \"amap-satellite\":\n                    // 高德卫星地图\n                    tileUrl = \"https://webst01.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}\";\n                    attribution = \"© 高德地图\";\n                    break;\n                case \"tencent\":\n                    // 腾讯地图\n                    tileUrl = \"https://rt1.map.gtimg.com/tile?z={z}&x={x}&y={-y}&styleid=0&version=256\";\n                    attribution = \"© 腾讯地图\";\n                    break;\n                default:\n                    tileUrl = \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\";\n                    attribution = \"© OpenStreetMap contributors\";\n            }\n            // 对于高德地图，需要特殊处理subdomains\n            const tileLayerOptions = {\n                attribution,\n                maxZoom: 18,\n                subdomains: mapService === \"amap\" ? [\n                    \"1\",\n                    \"2\",\n                    \"3\",\n                    \"4\"\n                ] : [\n                    \"a\",\n                    \"b\",\n                    \"c\"\n                ],\n                // 添加CSS类名以便后续样式处理\n                className: hideVillageMarkers ? 'hide-village-markers' : ''\n            };\n            leaflet__WEBPACK_IMPORTED_MODULE_2___default().tileLayer(tileUrl, tileLayerOptions).addTo(map);\n        }\n    }[\"FullScreenMap.useEffect\"], [\n        mapService,\n        hideVillageMarkers\n    ]);\n    // 更新热力图层 - 聚焦中国海域\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullScreenMap.useEffect\": ()=>{\n            if (!mapInstanceRef.current) return;\n            const map = mapInstanceRef.current;\n            // 移除现有热力图层\n            if (layersRef.current.heatmap) {\n                map.removeLayer(layersRef.current.heatmap);\n            }\n            if (showHeatmap) {\n                const heatmapLayer = leaflet__WEBPACK_IMPORTED_MODULE_2___default().layerGroup();\n                // 中国海域热力图实现\n                const createChinaMarineHeatmap = {\n                    \"FullScreenMap.useEffect.createChinaMarineHeatmap\": ()=>{\n                        const currentZoom = map.getZoom();\n                        const bounds = map.getBounds();\n                        // 根据缩放级别动态调整网格大小\n                        let gridSize = 1.0 // 基础网格大小\n                        ;\n                        if (currentZoom >= 8) gridSize = 0.2;\n                        else if (currentZoom >= 6) gridSize = 0.4;\n                        else if (currentZoom >= 4) gridSize = 0.8;\n                        else gridSize = 1.2;\n                        // 中国海域热点数据 - 包含台湾海域\n                        const chinaMarineHotspots = [\n                            // 渤海湾区域\n                            {\n                                lat: 39.0,\n                                lng: 117.5,\n                                intensity: 0.8,\n                                radius: 4,\n                                name: \"渤海湾\"\n                            },\n                            {\n                                lat: 38.5,\n                                lng: 119.0,\n                                intensity: 0.7,\n                                radius: 3,\n                                name: \"渤海中部\"\n                            },\n                            {\n                                lat: 40.0,\n                                lng: 120.5,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"辽东湾\"\n                            },\n                            // 黄海区域\n                            {\n                                lat: 36.0,\n                                lng: 120.0,\n                                intensity: 0.9,\n                                radius: 5,\n                                name: \"黄海南部\"\n                            },\n                            {\n                                lat: 35.0,\n                                lng: 119.5,\n                                intensity: 0.8,\n                                radius: 4,\n                                name: \"黄海中部\"\n                            },\n                            {\n                                lat: 37.5,\n                                lng: 122.0,\n                                intensity: 0.7,\n                                radius: 3,\n                                name: \"黄海北部\"\n                            },\n                            // 东海区域\n                            {\n                                lat: 31.0,\n                                lng: 122.0,\n                                intensity: 0.9,\n                                radius: 5,\n                                name: \"长江口\"\n                            },\n                            {\n                                lat: 29.5,\n                                lng: 122.5,\n                                intensity: 0.8,\n                                radius: 4,\n                                name: \"东海中部\"\n                            },\n                            {\n                                lat: 28.0,\n                                lng: 121.0,\n                                intensity: 0.7,\n                                radius: 4,\n                                name: \"东海南部\"\n                            },\n                            {\n                                lat: 30.0,\n                                lng: 123.5,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"东海外海\"\n                            },\n                            // 台湾海峡及周边海域\n                            {\n                                lat: 24.5,\n                                lng: 118.5,\n                                intensity: 0.8,\n                                radius: 4,\n                                name: \"台湾海峡北部\"\n                            },\n                            {\n                                lat: 23.5,\n                                lng: 118.0,\n                                intensity: 0.7,\n                                radius: 3,\n                                name: \"台湾海峡中部\"\n                            },\n                            {\n                                lat: 22.5,\n                                lng: 117.5,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"台湾海峡南部\"\n                            },\n                            // 台湾东部海域\n                            {\n                                lat: 24.0,\n                                lng: 121.5,\n                                intensity: 0.8,\n                                radius: 4,\n                                name: \"台湾东海岸\"\n                            },\n                            {\n                                lat: 23.0,\n                                lng: 121.3,\n                                intensity: 0.7,\n                                radius: 3,\n                                name: \"花莲外海\"\n                            },\n                            {\n                                lat: 22.0,\n                                lng: 121.0,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"台东外海\"\n                            },\n                            // 南海北部区域\n                            {\n                                lat: 22.0,\n                                lng: 114.0,\n                                intensity: 0.9,\n                                radius: 5,\n                                name: \"珠江口\"\n                            },\n                            {\n                                lat: 21.0,\n                                lng: 112.0,\n                                intensity: 0.8,\n                                radius: 4,\n                                name: \"南海北部\"\n                            },\n                            {\n                                lat: 20.0,\n                                lng: 111.0,\n                                intensity: 0.7,\n                                radius: 4,\n                                name: \"海南北部\"\n                            },\n                            {\n                                lat: 18.5,\n                                lng: 109.5,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"海南西部\"\n                            },\n                            // 南海中部区域\n                            {\n                                lat: 16.0,\n                                lng: 112.0,\n                                intensity: 0.7,\n                                radius: 4,\n                                name: \"西沙群岛\"\n                            },\n                            {\n                                lat: 15.0,\n                                lng: 113.5,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"南海中部\"\n                            },\n                            {\n                                lat: 12.0,\n                                lng: 114.0,\n                                intensity: 0.5,\n                                radius: 3,\n                                name: \"南沙群岛北部\"\n                            },\n                            // 南海南部区域\n                            {\n                                lat: 9.0,\n                                lng: 114.5,\n                                intensity: 0.6,\n                                radius: 3,\n                                name: \"南沙群岛\"\n                            },\n                            {\n                                lat: 8.0,\n                                lng: 112.0,\n                                intensity: 0.5,\n                                radius: 2,\n                                name: \"南海南部\"\n                            },\n                            // 钓鱼岛及周边海域\n                            {\n                                lat: 25.7,\n                                lng: 123.5,\n                                intensity: 0.7,\n                                radius: 2,\n                                name: \"钓鱼岛海域\"\n                            }\n                        ];\n                        // 创建网格映射以避免重复瓦片\n                        const gridMap = new Map();\n                        // 处理每个热点并生成周围的网格瓦片\n                        chinaMarineHotspots.forEach({\n                            \"FullScreenMap.useEffect.createChinaMarineHeatmap\": (hotspot)=>{\n                                const maxRadius = Math.min(hotspot.radius, 6);\n                                for(let latOffset = -maxRadius; latOffset <= maxRadius; latOffset++){\n                                    for(let lngOffset = -maxRadius; lngOffset <= maxRadius; lngOffset++){\n                                        const distance = Math.sqrt(latOffset * latOffset + lngOffset * lngOffset);\n                                        if (distance <= maxRadius) {\n                                            const cellLat = hotspot.lat + latOffset * gridSize;\n                                            const cellLng = hotspot.lng + lngOffset * gridSize;\n                                            // 对齐到网格\n                                            const gridLat = Math.round(cellLat / gridSize) * gridSize;\n                                            const gridLng = Math.round(cellLng / gridSize) * gridSize;\n                                            const gridKey = \"\".concat(gridLat.toFixed(3), \"_\").concat(gridLng.toFixed(3));\n                                            // 基于距离计算强度\n                                            const distanceRatio = 1 - distance / maxRadius;\n                                            // 使用位置坐标作为种子生成固定的伪随机值，避免Math.random()\n                                            const seed = (gridLat * 1000 + gridLng * 1000) % 1000;\n                                            const pseudoRandom = (seed * 9301 + 49297) % 233280 / 233280;\n                                            const cellIntensity = hotspot.intensity * distanceRatio * (0.5 + pseudoRandom * 0.5);\n                                            // 只保留有意义的强度值\n                                            if (cellIntensity >= 0.3) {\n                                                if (gridMap.has(gridKey)) {\n                                                    const existing = gridMap.get(gridKey);\n                                                    existing.intensity = Math.min(1.0, existing.intensity + cellIntensity * 0.4);\n                                                    existing.count++;\n                                                } else {\n                                                    gridMap.set(gridKey, {\n                                                        intensity: cellIntensity,\n                                                        count: 1,\n                                                        name: hotspot.name\n                                                    });\n                                                }\n                                            }\n                                        }\n                                    }\n                                }\n                            }\n                        }[\"FullScreenMap.useEffect.createChinaMarineHeatmap\"]);\n                        // 渲染网格瓦片\n                        const tileBatch = [];\n                        gridMap.forEach({\n                            \"FullScreenMap.useEffect.createChinaMarineHeatmap\": (data, gridKey)=>{\n                                const [latStr, lngStr] = gridKey.split(\"_\");\n                                const gridLat = Number.parseFloat(latStr);\n                                const gridLng = Number.parseFloat(lngStr);\n                                // 跳过低强度瓦片 - 只显示有颜色的\n                                if (data.intensity < 0.35) return;\n                                // 检查是否在当前视图范围内\n                                const buffer = gridSize * 2;\n                                if (gridLat < bounds.getSouth() - buffer || gridLat > bounds.getNorth() + buffer || gridLng < bounds.getWest() - buffer || gridLng > bounds.getEast() + buffer) {\n                                    return;\n                                }\n                                // 定义方形边界\n                                const tileBounds = [\n                                    [\n                                        gridLat - gridSize / 2,\n                                        gridLng - gridSize / 2\n                                    ],\n                                    [\n                                        gridLat + gridSize / 2,\n                                        gridLng + gridSize / 2\n                                    ]\n                                ];\n                                // 颜色映射 - 去除白色和浅色\n                                let color = \"#10b981\" // 默认翠绿色\n                                ;\n                                let speciesCount = Math.floor(data.intensity * 500);\n                                if (data.intensity >= 0.9) {\n                                    color = \"#dc2626\" // 红色 450+\n                                    ;\n                                    speciesCount = Math.floor(450 + data.intensity * 150);\n                                } else if (data.intensity >= 0.8) {\n                                    color = \"#ea580c\" // 橙红色 400-450\n                                    ;\n                                } else if (data.intensity >= 0.7) {\n                                    color = \"#f97316\" // 橙色 350-400\n                                    ;\n                                } else if (data.intensity >= 0.6) {\n                                    color = \"#f59e0b\" // 琥珀色 300-350\n                                    ;\n                                } else if (data.intensity >= 0.5) {\n                                    color = \"#eab308\" // 黄色 250-300\n                                    ;\n                                } else if (data.intensity >= 0.45) {\n                                    color = \"#84cc16\" // 青柠色 200-250\n                                    ;\n                                } else if (data.intensity >= 0.4) {\n                                    color = \"#22c55e\" // 绿色 150-200\n                                    ;\n                                } else {\n                                    color = \"#10b981\" // 翠绿色 100-150\n                                    ;\n                                }\n                                // 创建矩形瓦片\n                                const rectangle = leaflet__WEBPACK_IMPORTED_MODULE_2___default().rectangle(tileBounds, {\n                                    color: color,\n                                    fillColor: color,\n                                    fillOpacity: Math.min(data.intensity * (heatmapOpacity / 100), 0.8),\n                                    weight: 0,\n                                    stroke: false,\n                                    interactive: true\n                                });\n                                // 悬停效果\n                                rectangle.on(\"mouseover\", {\n                                    \"FullScreenMap.useEffect.createChinaMarineHeatmap\": function() {\n                                        this.setStyle({\n                                            fillOpacity: Math.min(data.intensity * (heatmapOpacity / 100) + 0.15, 0.9)\n                                        });\n                                    }\n                                }[\"FullScreenMap.useEffect.createChinaMarineHeatmap\"]);\n                                rectangle.on(\"mouseout\", {\n                                    \"FullScreenMap.useEffect.createChinaMarineHeatmap\": function() {\n                                        this.setStyle({\n                                            fillOpacity: Math.min(data.intensity * (heatmapOpacity / 100), 0.8)\n                                        });\n                                    }\n                                }[\"FullScreenMap.useEffect.createChinaMarineHeatmap\"]);\n                                // 详细弹窗\n                                rectangle.bindPopup('\\n            <div class=\"p-3 min-w-52\">\\n              <h4 class=\"font-semibold mb-2 text-blue-800\">'.concat(data.name, '海域</h4>\\n              <div class=\"space-y-2 text-sm\">\\n                <div class=\"flex justify-between items-center\">\\n                  <span class=\"text-gray-600\">海洋物种:</span>\\n                  <span class=\"font-bold text-lg\" style=\"color: ').concat(color, '\">').concat(speciesCount, '种</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">声学记录:</span>\\n                  <span class=\"font-medium\">').concat(Math.floor(speciesCount * 2.8), '条</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">生物密度:</span>\\n                  <span class=\"font-medium\">').concat(data.intensity >= 0.8 ? \"极高\" : data.intensity >= 0.6 ? \"高\" : data.intensity >= 0.45 ? \"中\" : \"中低\", '</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">海域坐标:</span>\\n                  <span class=\"font-mono text-xs\">').concat(gridLat.toFixed(2), \"\\xb0N, \").concat(gridLng.toFixed(2), '\\xb0E</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">监测站点:</span>\\n                  <span class=\"text-xs\">').concat(data.count, '个</span>\\n                </div>\\n              </div>\\n              <div class=\"mt-3 flex space-x-2\">\\n                <button class=\"px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors\">\\n                  详细数据\\n                </button>\\n                <button class=\"px-3 py-1 bg-green-500 text-white text-xs rounded hover:bg-green-600 transition-colors\">\\n                  播放录音\\n                </button>\\n              </div>\\n            </div>\\n          '), {\n                                    maxWidth: 320,\n                                    className: \"custom-popup\"\n                                });\n                                tileBatch.push(rectangle);\n                            }\n                        }[\"FullScreenMap.useEffect.createChinaMarineHeatmap\"]);\n                        // 批量添加瓦片\n                        tileBatch.forEach({\n                            \"FullScreenMap.useEffect.createChinaMarineHeatmap\": (tile)=>tile.addTo(heatmapLayer)\n                        }[\"FullScreenMap.useEffect.createChinaMarineHeatmap\"]);\n                        console.log(\"渲染了 \".concat(tileBatch.length, \" 个中国海域热力图瓦片，缩放级别 \").concat(currentZoom));\n                    }\n                }[\"FullScreenMap.useEffect.createChinaMarineHeatmap\"];\n                createChinaMarineHeatmap();\n                // 地图移动或缩放时更新热力图\n                const updateHeatmapHandler = leaflet__WEBPACK_IMPORTED_MODULE_2___default().Util.throttle({\n                    \"FullScreenMap.useEffect.updateHeatmapHandler\": ()=>{\n                        if (showHeatmap && layersRef.current.heatmap) {\n                            map.removeLayer(layersRef.current.heatmap);\n                            const newHeatmapLayer = leaflet__WEBPACK_IMPORTED_MODULE_2___default().layerGroup();\n                            layersRef.current.heatmap = newHeatmapLayer;\n                            createChinaMarineHeatmap();\n                            newHeatmapLayer.addTo(map);\n                        }\n                    }\n                }[\"FullScreenMap.useEffect.updateHeatmapHandler\"], 400);\n                map.on(\"zoomend moveend\", updateHeatmapHandler);\n                layersRef.current.heatmap = heatmapLayer;\n                heatmapLayer.addTo(map);\n                return ({\n                    \"FullScreenMap.useEffect\": ()=>{\n                        map.off(\"zoomend moveend\", updateHeatmapHandler);\n                    }\n                })[\"FullScreenMap.useEffect\"];\n            }\n        }\n    }[\"FullScreenMap.useEffect\"], [\n        showHeatmap,\n        heatmapOpacity,\n        selectedSpecies,\n        selectedTimeRange\n    ]);\n    // 更新分布图层\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullScreenMap.useEffect\": ()=>{\n            if (!mapInstanceRef.current) return;\n            const map = mapInstanceRef.current;\n            if (layersRef.current.distribution) {\n                map.removeLayer(layersRef.current.distribution);\n            }\n            if (showDistribution) {\n                const distributionLayer = leaflet__WEBPACK_IMPORTED_MODULE_2___default().layerGroup();\n                // 中国海域物种分布区域\n                const chinaDistributionAreas = [\n                    {\n                        lat: 38.0,\n                        lng: 119.0,\n                        radius: 300000,\n                        name: \"渤海黄海鲸豚分布区\"\n                    },\n                    {\n                        lat: 30.0,\n                        lng: 122.0,\n                        radius: 400000,\n                        name: \"东海江豚分布区\"\n                    },\n                    {\n                        lat: 24.0,\n                        lng: 118.5,\n                        radius: 350000,\n                        name: \"台湾海峡中华白海豚分布区\"\n                    },\n                    {\n                        lat: 20.0,\n                        lng: 112.0,\n                        radius: 500000,\n                        name: \"南海鲸类分布区\"\n                    }\n                ];\n                chinaDistributionAreas.forEach({\n                    \"FullScreenMap.useEffect\": (area, index)=>{\n                        const colors = [\n                            \"#3b82f6\",\n                            \"#10b981\",\n                            \"#f59e0b\",\n                            \"#ef4444\"\n                        ];\n                        leaflet__WEBPACK_IMPORTED_MODULE_2___default().circle([\n                            area.lat,\n                            area.lng\n                        ], {\n                            color: colors[index % colors.length],\n                            fillColor: colors[index % colors.length],\n                            fillOpacity: 0.15,\n                            radius: area.radius,\n                            weight: 2\n                        }).addTo(distributionLayer).bindPopup('\\n          <div class=\"p-2\">\\n            <h4 class=\"font-semibold\">'.concat(area.name, '</h4>\\n            <p class=\"text-sm text-gray-600\">重要海洋生物栖息和活动区域</p>\\n          </div>\\n        '));\n                    }\n                }[\"FullScreenMap.useEffect\"]);\n                layersRef.current.distribution = distributionLayer;\n                distributionLayer.addTo(map);\n            }\n        }\n    }[\"FullScreenMap.useEffect\"], [\n        showDistribution\n    ]);\n    // 更新采集点图层\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullScreenMap.useEffect\": ()=>{\n            if (!mapInstanceRef.current) return;\n            const map = mapInstanceRef.current;\n            if (layersRef.current.collectionPoints) {\n                map.removeLayer(layersRef.current.collectionPoints);\n            }\n            if (showCollectionPoints) {\n                const collectionPointsLayer = leaflet__WEBPACK_IMPORTED_MODULE_2___default().layerGroup();\n                // 中国海域采集点数据\n                const chinaCollectionPoints = [\n                    {\n                        lat: 39.0,\n                        lng: 117.8,\n                        species: \"江豚\",\n                        recordings: 45,\n                        date: \"2024-01-15\"\n                    },\n                    {\n                        lat: 31.2,\n                        lng: 121.8,\n                        species: \"中华白海豚\",\n                        recordings: 32,\n                        date: \"2024-01-14\"\n                    },\n                    {\n                        lat: 24.5,\n                        lng: 118.2,\n                        species: \"台湾白海豚\",\n                        recordings: 28,\n                        date: \"2024-01-13\"\n                    },\n                    {\n                        lat: 22.3,\n                        lng: 114.1,\n                        species: \"中华白海豚\",\n                        recordings: 38,\n                        date: \"2024-01-12\"\n                    },\n                    {\n                        lat: 20.2,\n                        lng: 110.8,\n                        species: \"热带海豚\",\n                        recordings: 25,\n                        date: \"2024-01-11\"\n                    },\n                    {\n                        lat: 16.8,\n                        lng: 112.3,\n                        species: \"西沙鲸类\",\n                        recordings: 19,\n                        date: \"2024-01-10\"\n                    }\n                ];\n                chinaCollectionPoints.forEach({\n                    \"FullScreenMap.useEffect\": (point)=>{\n                        leaflet__WEBPACK_IMPORTED_MODULE_2___default().marker([\n                            point.lat,\n                            point.lng\n                        ]).addTo(collectionPointsLayer).bindPopup('\\n            <div class=\"p-3 min-w-48\">\\n              <h4 class=\"font-semibold mb-2\">'.concat(point.species, '监测点</h4>\\n              <div class=\"space-y-1 text-sm\">\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">声学记录:</span>\\n                  <span class=\"font-medium\">').concat(point.recordings, '条</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">最近更新:</span>\\n                  <span class=\"font-medium\">').concat(point.date, '</span>\\n                </div>\\n                <div class=\"flex justify-between\">\\n                  <span class=\"text-gray-600\">坐标:</span>\\n                  <span class=\"font-mono text-xs\">').concat(point.lat.toFixed(2), \"\\xb0N, \").concat(point.lng.toFixed(2), '\\xb0E</span>\\n                </div>\\n              </div>\\n              <div class=\"mt-3 flex space-x-2\">\\n                <button class=\"px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600\">\\n                  播放录音\\n                </button>\\n                <button class=\"px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600\">\\n                  查看详情\\n                </button>\\n              </div>\\n            </div>\\n          '));\n                    }\n                }[\"FullScreenMap.useEffect\"]);\n                layersRef.current.collectionPoints = collectionPointsLayer;\n                collectionPointsLayer.addTo(map);\n            }\n        }\n    }[\"FullScreenMap.useEffect\"], [\n        showCollectionPoints,\n        selectedSpecies,\n        selectedTimeRange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: mapRef,\n                className: \"w-full h-full \".concat(hideVillageMarkers ? 'hide-village-markers' : ''),\n                \"data-map-service\": mapService,\n                \"data-zoom-level\": \"medium\",\n                style: {\n                    // 添加CSS过滤器来隐藏白色方块村庄标记\n                    ...hideVillageMarkers && {\n                        filter: 'contrast(1.1) saturate(1.05)'\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map\\\\full-screen-map.tsx\",\n                lineNumber: 720,\n                columnNumber: 7\n            }, this),\n            showLayerControl && markerManager && showMarkerLayers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-4 right-4 z-[1000]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layer_control_panel__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    markerManager: markerManager,\n                    compact: false,\n                    className: \"w-80 max-h-96 overflow-y-auto\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map\\\\full-screen-map.tsx\",\n                    lineNumber: 736,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map\\\\full-screen-map.tsx\",\n                lineNumber: 735,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\workspaces\\\\dd\\\\mbdp\\\\frontend\\\\components\\\\map\\\\full-screen-map.tsx\",\n        lineNumber: 719,\n        columnNumber: 5\n    }, this);\n}\n_s(FullScreenMap, \"grOti/9O8OH7VDN7G7UeNn+bnt0=\", false, function() {\n    return [\n        _marker_layer_manager__WEBPACK_IMPORTED_MODULE_4__.useMarkerLayerManager\n    ];\n});\n_c = FullScreenMap;\nvar _c;\n$RefreshReg$(_c, \"FullScreenMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/map/full-screen-map.tsx\n"));

/***/ })

});